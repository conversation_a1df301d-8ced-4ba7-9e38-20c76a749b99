//go:build integration
// +build integration

package it_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"

	parcelConst "git.ninjavan.co/3pl/configs/parcel"
	"git.ninjavan.co/3pl/httpmodels/order"
	"git.ninjavan.co/3pl/models"
	"git.ninjavan.co/3pl/repositories"
	"git.ninjavan.co/3pl/testutils"
)

func (s *ITLazadaMMOrderCreator) TestLazadaAIDCMMOrderCreator_CreateMMCCParcel() {
	const (
		partnerID = 88426
	)

	partner, err := repositories.NewPartnerRepository().
		GetOne(context.TODO(), models.PartnerWhere.ID.EQ(partnerID))
	if err != nil {
		s.T().Error(err)
		return
	}

	s.T().Run("Create mmcc parcel with valid request", func(t *testing.T) {
		mmccParcel := &order.BaseRequest{
			RequestId: uuid.New().String(),
			From: order.Address{
				Name:          "Myint Maung",
				AddressLine1:  "No.171, Market Street, Southern District, Naypyidaw",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Mandalay Region"),
				CountryCode:   "SG",
				PostCode:      lo.ToPtr(""),
				ContactNumber: lo.ToPtr("+95831015052"),
				ContactEmail:  lo.ToPtr(""),
			},
			To: order.Address{
				Name:          "Myint Thet",
				AddressLine1:  "No.361, Palace Road, Southern District, Yangon",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Bago Region"),
				CountryCode:   "PH",
				PostCode:      lo.ToPtr("297311"),
				ContactNumber: lo.ToPtr("+95090617001"),
				ContactEmail:  lo.ToPtr("<EMAIL>"),
			},
			ParcelDetails: &models.ParcelDetails{
				ShipperSubmittedWeight: lo.ToPtr(9.571673482077689),
				Weight:                 lo.ToPtr(9.571673482077689),
				Value:                  lo.ToPtr(905.************),
				CustomsCurrency:        lo.ToPtr("USD"),
				Quantity:               lo.ToPtr[uint](2),
				ShipperSubmittedDimensions: &models.Dimensions{
					Length: lo.ToPtr(41.47050153553845),
					Width:  lo.ToPtr(8.327161496651012),
					Height: lo.ToPtr(49.011430382809415),
				},
			},
			Items: order.ParcelItemSlice{
				{
					Description: lo.ToPtr("Item 1"),
					UnitValue:   lo.ToPtr(201.1),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.1),
				},
				{
					Description: lo.ToPtr("Item 2"),
					UnitValue:   lo.ToPtr(201.2),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.2),
				},
			},
			PartnerUniqueKey: lo.ToPtr("LZDCC-PARCEL-UNIQ1747033508893"),
		}

		err = s.creator.CreateMMCCParcelAIDC(context.Background(), partner, mmccParcel)
		if err != nil {
			t.Error(err)
			return
		}

		p, err := repositories.NewParcelRepository().GetOneByIdentifier(context.Background(), repositories.NewMMCCParcelIdentifier(partnerID, *mmccParcel.PartnerUniqueKey))
		if err != nil {
			t.Error(err)
			return
		}

		tobeParcel := models.Parcel{
			PartnerID:         null.Uint64From(partnerID),
			Type:              parcelConst.MMCCParcel,
			FromName:          null.StringFrom("Myint Maung"),
			FromAddressLine1:  null.StringFrom("No.171, Market Street, Southern District, Naypyidaw"),
			FromCity:          null.StringFrom("Mawlamyine"),
			FromStateProvince: null.StringFrom("Mandalay Region"),
			FromCountryCode:   null.StringFrom("SG"),
			FromPostcode:      null.StringFrom(""),
			FromContactNumber: null.StringFrom("+95831015052"),
			ToName:            null.StringFrom("Myint Thet"),
			ToAddressLine1:    null.StringFrom("No.361, Palace Road, Southern District, Yangon"),
			ToCity:            null.StringFrom("Mawlamyine"),
			ToStateProvince:   null.StringFrom("Bago Region"),
			ToCountryCode:     null.StringFrom("PH"),
			ToPostcode:        null.StringFrom("297311"),
		}
		if !testutils.CompareNonEmptyFields(p, tobeParcel) {
			t.Error(fmt.Errorf("Expected parcel to be created %+v, but got %+v", tobeParcel, p))
			return
		}
	})

	s.T().Run("Create mmcc parcel with the same idempotent ID", func(t *testing.T) {
		// Reuse the same parcel data from the previous test
		mmccParcel := &order.BaseRequest{
			RequestId: uuid.New().String(),
			From: order.Address{
				Name:          "Myint Maung",
				AddressLine1:  "No.171, Market Street, Southern District, Naypyidaw",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Mandalay Region"),
				CountryCode:   "SG",
				PostCode:      lo.ToPtr(""),
				ContactNumber: lo.ToPtr("+95831015052"),
				ContactEmail:  lo.ToPtr(""),
			},
			To: order.Address{
				Name:          "Myint Thet",
				AddressLine1:  "No.361, Palace Road, Southern District, Yangon",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Bago Region"),
				CountryCode:   "PH",
				PostCode:      lo.ToPtr("297311"),
				ContactNumber: lo.ToPtr("+95090617001"),
				ContactEmail:  lo.ToPtr("<EMAIL>"),
			},
			ParcelDetails: &models.ParcelDetails{
				ShipperSubmittedWeight: lo.ToPtr(9.571673482077689),
				Weight:                 lo.ToPtr(9.571673482077689),
				Value:                  lo.ToPtr(905.************),
				CustomsCurrency:        lo.ToPtr("USD"),
				Quantity:               lo.ToPtr[uint](2),
				ShipperSubmittedDimensions: &models.Dimensions{
					Length: lo.ToPtr(41.47050153553845),
					Width:  lo.ToPtr(8.327161496651012),
					Height: lo.ToPtr(49.011430382809415),
				},
			},
			Items: order.ParcelItemSlice{
				{
					Description: lo.ToPtr("Item 1"),
					UnitValue:   lo.ToPtr(201.1),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.1),
				},
				{
					Description: lo.ToPtr("Item 2"),
					UnitValue:   lo.ToPtr(201.2),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.2),
				},
			},
			PartnerUniqueKey: lo.ToPtr(uuid.New().String()),
		}

		// First creation attempt
		err = s.creator.CreateMMCCParcelAIDC(context.Background(), partner, mmccParcel)
		if err != nil {
			t.Error(err)
			return
		}

		// Get the first parcel
		firstParcel, err := repositories.NewParcelRepository().GetOneByIdentifier(context.Background(), repositories.NewMMCCParcelIdentifier(partnerID, *mmccParcel.PartnerUniqueKey))
		if err != nil {
			t.Error(err)
			return
		}

		// Second creation attempt with the same data
		err = s.creator.CreateMMCCParcelAIDC(context.Background(), partner, mmccParcel)
		if err != nil {
			t.Error(err)
			return
		}

		// Get the parcel after second attempt
		secondParcel, err := repositories.NewParcelRepository().GetOneByIdentifier(context.Background(), repositories.NewMMCCParcelIdentifier(partnerID, *mmccParcel.PartnerUniqueKey))
		if err != nil {
			t.Error(err)
			return
		}

		// Verify that both parcels are the same (idempotency)
		if firstParcel.ID != secondParcel.ID {
			t.Error(fmt.Errorf("Expected idempotent creation to return the same parcel, but got different IDs: %d vs %d", firstParcel.ID, secondParcel.ID))
			return
		}
	})
}

func (s *ITLazadaMMOrderCreator) TestLazadaAIDCMMOrderCreator_CreateAIDCMMCCBag() {
	const (
		partnerID          = 88426
		refTrackingID1     = "LZDCCPARCEL1"
		lzdTrackingNumber1 = "UNIQ1747033508893"
		refTrackingID2     = "LZDCCPARCEL2"
		lzdTrackingNumber2 = "UNIQ1747033508894"
	)

	partner, err := repositories.NewPartnerRepository().
		GetOne(context.TODO(), models.PartnerWhere.ID.EQ(partnerID))
	if err != nil {
		s.T().Error(err)
		return
	}

	s.T().Run("Create AIDC MMCC bag with valid request", func(t *testing.T) {

		parcel1 := &order.BaseRequest{
			RequestId: uuid.New().String(),
			From: order.Address{
				Name:          "Myint Maung",
				AddressLine1:  "No.171, Market Street, Southern District, Naypyidaw",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Mandalay Region"),
				CountryCode:   "SG",
				PostCode:      lo.ToPtr(""),
				ContactNumber: lo.ToPtr("+95831015052"),
				ContactEmail:  lo.ToPtr(""),
			},
			To: order.Address{
				Name:          "Myint Thet",
				AddressLine1:  "No.361, Palace Road, Southern District, Yangon",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Bago Region"),
				CountryCode:   "PH",
				PostCode:      lo.ToPtr("297311"),
				ContactNumber: lo.ToPtr("+95090617001"),
				ContactEmail:  lo.ToPtr("<EMAIL>"),
			},
			ParcelDetails: &models.ParcelDetails{
				ShipperSubmittedWeight: lo.ToPtr(9.571673482077689),
				Weight:                 lo.ToPtr(9.571673482077689),
				Value:                  lo.ToPtr(905.************),
				CustomsCurrency:        lo.ToPtr("USD"),
				Quantity:               lo.ToPtr[uint](2),
				ShipperSubmittedDimensions: &models.Dimensions{
					Length: lo.ToPtr(41.47050153553845),
					Width:  lo.ToPtr(8.327161496651012),
					Height: lo.ToPtr(49.011430382809415),
				},
			},
			Items: order.ParcelItemSlice{
				{
					Description: lo.ToPtr("Item 1"),
					UnitValue:   lo.ToPtr(201.1),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.1),
				},
				{
					Description: lo.ToPtr("Item 2"),
					UnitValue:   lo.ToPtr(201.2),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.2),
				},
			},
			PartnerUniqueKey: lo.ToPtr(fmt.Sprintf("%s_%s", refTrackingID1, lzdTrackingNumber1)),
			RefTrackingID:    lo.ToPtr(refTrackingID1),
		}

		parcel2 := &order.BaseRequest{
			RequestId: uuid.New().String(),
			From: order.Address{
				Name:          "Myint Maung",
				AddressLine1:  "No.171, Market Street, Southern District, Naypyidaw",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Mandalay Region"),
				CountryCode:   "SG",
				PostCode:      lo.ToPtr(""),
				ContactNumber: lo.ToPtr("+95831015052"),
				ContactEmail:  lo.ToPtr(""),
			},
			To: order.Address{
				Name:          "Myint Thet",
				AddressLine1:  "No.361, Palace Road, Southern District, Yangon",
				City:          lo.ToPtr("Mawlamyine"),
				StateProvince: lo.ToPtr("Bago Region"),
				CountryCode:   "PH",
				PostCode:      lo.ToPtr("297311"),
				ContactNumber: lo.ToPtr("+95090617001"),
				ContactEmail:  lo.ToPtr("<EMAIL>"),
			},
			ParcelDetails: &models.ParcelDetails{
				ShipperSubmittedWeight: lo.ToPtr(9.571673482077689),
				Weight:                 lo.ToPtr(9.571673482077689),
				Value:                  lo.ToPtr(905.************),
				CustomsCurrency:        lo.ToPtr("USD"),
				Quantity:               lo.ToPtr[uint](2),
				ShipperSubmittedDimensions: &models.Dimensions{
					Length: lo.ToPtr(41.47050153553845),
					Width:  lo.ToPtr(8.327161496651012),
					Height: lo.ToPtr(49.011430382809415),
				},
			},
			Items: order.ParcelItemSlice{
				{
					Description: lo.ToPtr("Item 1"),
					UnitValue:   lo.ToPtr(201.1),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.1),
				},
				{
					Description: lo.ToPtr("Item 2"),
					UnitValue:   lo.ToPtr(201.2),
					Quantity:    lo.ToPtr(3),
					UnitWeight:  lo.ToPtr(9.2),
				},
			},
			PartnerUniqueKey: lo.ToPtr(fmt.Sprintf("%s_%s", refTrackingID2, lzdTrackingNumber2)),
			RefTrackingID:    lo.ToPtr(refTrackingID2),
		}

		// Create parcels first
		err = s.creator.CreateMMCCParcelAIDC(context.Background(), partner, parcel1)
		if err != nil {
			t.Error(err)
			return
		}

		err = s.creator.CreateMMCCParcelAIDC(context.Background(), partner, parcel2)
		if err != nil {
			t.Error(err)
			return
		}

		// Create bag request
		bagReq := &order.LazadaAIDCMMCreateBagRequest{
			IdempotentId:   uuid.New().String(),
			BigBagID:       "LZDCC-BAG-UNIQ1747033508895",
			TrackingNumber: "LZDCC-TRACK-1747033508895",
			CargoType:      2,
			ToLocation:     "BKI",
			BigBagWeight:   19143, // Convert to grams (19.143346964155378 kg * 1000)
			WeightUnit:     "g",
			BigBagLength:   41470, // Convert to mm (41.47050153553845 cm * 10)
			BigBagWidth:    8327,  // Convert to mm (8.327161496651012 cm * 10)
			BigBagHeight:   49011, // Convert to mm (49.011430382809415 cm * 10)
			DimensionUnit:  "mm",
			ParcelQty:      2,
			ParcelList: []*order.LazadaMMParcel{
				{
					OrderCode:      refTrackingID1,
					TrackingNumber: lzdTrackingNumber1,
				},
				{
					OrderCode:      refTrackingID2,
					TrackingNumber: lzdTrackingNumber2,
				},
			},
		}

		// Create bag
		bag, err := s.creator.CreateAIDCMMCCBag(context.Background(), partner, bagReq)
		if err != nil {
			t.Error(err)
			return
		}

		// Verify bag creation
		expectedBag := models.Parcel{
			PartnerID:         null.Uint64From(partnerID),
			Type:              parcelConst.BagB2CV2,
			SourceOrderID:     null.StringFrom(bagReq.BigBagID),
			RefTrackingID:     null.StringFrom(bagReq.TrackingNumber),
			ServiceID:         null.UintFrom(81904),
			ProductID:         null.UintFrom(141019),
			FromName:          null.StringFrom("Ninjalogistics"),
			FromAddressLine1:  null.StringFrom("金港北三路6号"),
			FromAddressLine2:  null.StringFrom("龙地广州空港物流园"),
			FromAddressLine3:  null.StringFrom("P栋311"),
			FromAddressLine4:  null.StringFrom("空港花都"),
			FromCity:          null.StringFrom("广州市"),
			FromStateProvince: null.StringFrom("广东省"),
			FromCountryCode:   null.StringFrom("CN"),
			FromPostcode:      null.StringFrom("510801"),
			ToName:            null.StringFrom("Ninjalogistics"),
			ToAddressLine1:    null.StringFrom("Lot 1-8, Persiaran Jubli Perak"),
			ToAddressLine2:    null.StringFrom("Seksyen 22"),
			ToCity:            null.StringFrom("Shah Alam"),
			ToStateProvince:   null.StringFrom("Selangor Darul Ehsan"),
			ToCountryCode:     null.StringFrom("MY"),
			ToPostcode:        null.StringFrom("40300"),
		}

		if !testutils.CompareNonEmptyFields(bag, expectedBag) {
			t.Error(fmt.Errorf("Expected bag to be created %+v, but got %+v", expectedBag, bag))
			return
		}

		// Verify parcels are linked to bag
		parcels, err := repositories.NewParcelRepository().GetListWithCtx(context.Background(),
			models.ParcelWhere.ParentID.EQ(null.UintFrom(bag.ID)),
		)
		if err != nil {
			t.Error(err)
			return
		}

		if len(parcels) != 2 {
			t.Error(fmt.Errorf("Expected 2 parcels to be linked to bag, but got %d", len(parcels)))
			return
		}

		// Verify each parcel has correct bag linking
		for _, p := range parcels {
			if p.ParentID.Uint != bag.ID {
				t.Error(fmt.Errorf("Expected parcel %d to have parent ID %d, but got %d", p.ID, bag.ID, p.ParentID.Uint))
				return
			}
			if p.SourceOrderID.String != bagReq.BigBagID {
				t.Error(fmt.Errorf("Expected parcel %d to have source order ID %s, but got %s", p.ID, bagReq.BigBagID, p.SourceOrderID.String))
				return
			}
		}
	})
}
