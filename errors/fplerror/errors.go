package fplerror

import "errors"

var (
	RetryableErr           = errors.New("fpl-retryable")
	NonRetryableErr        = errors.New("fpl-non-retryable")
	ErrDuplicated          = errors.New("duplicated-request")
	ErrInvalidServiceCode  = errors.New("invalid-service-code")
	ErrBagNotFound         = errors.New("bag-not-found")
	BagCancelledErr        = errors.New("bag-cancelled")
	ErrOrderDuplicated     = errors.New("duplicated-order")
	ErrParcelAssignedToBag = errors.New("parcels-assigned-to-another-bag")
	ErrParcelNotFound      = errors.New("parcel-not-found")
)
